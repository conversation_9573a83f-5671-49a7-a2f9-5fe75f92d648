-- TaraDental Seed Data
-- This file contains initial data for the TaraDental platform
-- Execute this after creating the schema and RLS policies

-- =============================================================================
-- DAILY LESSONS SEED DATA
-- =============================================================================

INSERT INTO daily_lessons (id, title, description, content, lesson_type, difficulty, order_index, estimated_duration_minutes, xp_reward) VALUES
(
    uuid_generate_v4(),
    'Introduction to Dental Sales',
    'Learn the fundamentals of dental sales and patient communication',
    '{
        "sections": [
            {
                "type": "video",
                "title": "Welcome to Dental Sales",
                "content": "Introduction video about dental sales fundamentals",
                "duration": 300
            },
            {
                "type": "text",
                "title": "Key Principles",
                "content": "The foundation of successful dental sales lies in building trust, understanding patient needs, and providing value-based solutions."
            },
            {
                "type": "quiz",
                "title": "Knowledge Check",
                "questions": [
                    {
                        "question": "What is the most important aspect of dental sales?",
                        "options": ["Price", "Trust", "Speed", "Technology"],
                        "correct": 1
                    }
                ]
            }
        ]
    }'::jsonb,
    'video',
    'beginner',
    1,
    15,
    10
),
(
    uuid_generate_v4(),
    'Understanding Patient Psychology',
    'Explore the psychological factors that influence patient decisions',
    '{
        "sections": [
            {
                "type": "reading",
                "title": "Patient Mindset",
                "content": "Patients often come to dental offices with anxiety, fear, and financial concerns. Understanding these emotions is crucial for effective communication."
            },
            {
                "type": "challenge",
                "title": "Empathy Exercise",
                "content": "Practice identifying patient emotions and responding with empathy",
                "scenario": "A patient expresses concern about the cost of treatment"
            }
        ]
    }'::jsonb,
    'reading',
    'beginner',
    2,
    20,
    15
),
(
    uuid_generate_v4(),
    'The LAARC Framework Introduction',
    'Learn the Listen, Acknowledge, Assess, Respond, Confirm framework',
    '{
        "sections": [
            {
                "type": "video",
                "title": "LAARC Overview",
                "content": "Comprehensive introduction to the LAARC framework",
                "duration": 480
            },
            {
                "type": "text",
                "title": "Framework Breakdown",
                "content": "LAARC stands for Listen, Acknowledge, Assess, Respond, and Confirm. Each step is crucial for effective patient communication."
            },
            {
                "type": "quiz",
                "title": "LAARC Quiz",
                "questions": [
                    {
                        "question": "What does the A in LAARC stand for?",
                        "options": ["Ask", "Acknowledge", "Assess", "Answer"],
                        "correct": 1
                    },
                    {
                        "question": "Which comes first in the LAARC framework?",
                        "options": ["Acknowledge", "Listen", "Assess", "Respond"],
                        "correct": 1
                    }
                ]
            }
        ]
    }'::jsonb,
    'video',
    'intermediate',
    3,
    25,
    20
);

-- =============================================================================
-- PATIENT PERSONAS SEED DATA
-- =============================================================================

INSERT INTO patient_personas (id, name, age, background, personality_traits, medical_history, objection_likelihood, difficulty, voice_id) VALUES
(
    uuid_generate_v4(),
    'Sarah Johnson',
    34,
    'Working mother of two, budget-conscious, values family time',
    '["anxious", "practical", "family-oriented", "cost-conscious", "time-pressed"]'::jsonb,
    '{"dental_anxiety": "moderate", "previous_bad_experience": false, "insurance": "basic_coverage"}'::jsonb,
    60,
    'beginner',
    'sarah_voice_id'
),
(
    uuid_generate_v4(),
    'Robert Chen',
    45,
    'Business executive, detail-oriented, wants the best quality',
    '["analytical", "perfectionist", "quality-focused", "impatient", "decisive"]'::jsonb,
    '{"dental_anxiety": "low", "previous_bad_experience": false, "insurance": "premium_coverage"}'::jsonb,
    30,
    'intermediate',
    'robert_voice_id'
),
(
    uuid_generate_v4(),
    'Maria Rodriguez',
    28,
    'Graduate student, limited budget, health-conscious',
    '["health-conscious", "budget-limited", "research-oriented", "cautious", "environmentally-aware"]'::jsonb,
    '{"dental_anxiety": "high", "previous_bad_experience": true, "insurance": "student_plan"}'::jsonb,
    80,
    'advanced',
    'maria_voice_id'
);

-- =============================================================================
-- OBJECTION SCENARIOS SEED DATA
-- =============================================================================

INSERT INTO objection_scenarios (id, title, description, initial_objection, context, difficulty, category, laarc_guidance) VALUES
(
    uuid_generate_v4(),
    'Cost Concern - Basic Cleaning',
    'Patient objects to the cost of a routine cleaning',
    'This seems really expensive for just a cleaning. I can get it done cheaper elsewhere.',
    '{"treatment": "routine_cleaning", "cost": 150, "patient_type": "new_patient"}'::jsonb,
    'beginner',
    'cost',
    '{
        "listen": "Allow the patient to fully express their concern without interrupting",
        "acknowledge": "I understand that cost is an important consideration for you",
        "assess": "Ask about their previous dental experiences and what they value most",
        "respond": "Explain the comprehensive nature of your cleaning and the value provided",
        "confirm": "Check that they understand the value and ask if they have other concerns"
    }'::jsonb
),
(
    uuid_generate_v4(),
    'Time Constraint - Crown Procedure',
    'Busy professional concerned about time commitment for crown',
    'I dont have time for multiple appointments. Can we do this all in one visit?',
    '{"treatment": "crown", "appointments_needed": 2, "patient_type": "business_executive"}'::jsonb,
    'intermediate',
    'time',
    '{
        "listen": "Understand their specific time constraints and schedule pressures",
        "acknowledge": "I recognize that your time is valuable and scheduling is challenging",
        "assess": "Explore their flexibility and what times work best for them",
        "respond": "Explain the process and explore same-day crown options if available",
        "confirm": "Ensure they understand the timeline and are comfortable with the plan"
    }'::jsonb
),
(
    uuid_generate_v4(),
    'Pain Fear - Root Canal',
    'Patient terrified of pain during root canal procedure',
    'I heard root canals are extremely painful. I dont think I can handle that kind of pain.',
    '{"treatment": "root_canal", "pain_level_fear": "high", "previous_experience": "none"}'::jsonb,
    'advanced',
    'pain',
    '{
        "listen": "Allow them to express their fears and concerns about pain",
        "acknowledge": "Its completely understandable to be concerned about discomfort",
        "assess": "Ask about their pain tolerance and previous dental experiences",
        "respond": "Explain modern pain management techniques and sedation options",
        "confirm": "Make sure they feel confident about the pain management plan"
    }'::jsonb
);

-- =============================================================================
-- SAMPLE XP TRANSACTION TYPES DATA
-- =============================================================================

-- Note: XP transactions will be created dynamically as users complete activities
-- This is just for reference of the types of transactions that will occur

-- Example XP values for different activities:
-- lesson_complete: 10-25 XP depending on difficulty
-- quiz_pass: 5-15 XP depending on score
-- challenge_complete: 15-30 XP depending on difficulty
-- streak_bonus: 5 XP per day of streak
-- roleplay_complete: 20-50 XP depending on performance
-- daily_login: 2 XP

-- =============================================================================
-- UTILITY FUNCTIONS FOR SEED DATA
-- =============================================================================

-- Note: User creation is handled by Supabase Auth
-- Sample users should be created through the authentication system

-- Function to award XP to a user
CREATE OR REPLACE FUNCTION award_xp(
    user_id UUID,
    xp_amount INTEGER,
    transaction_type xp_transaction_type,
    description TEXT DEFAULT NULL,
    related_lesson_id UUID DEFAULT NULL,
    related_session_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    -- Insert XP transaction
    INSERT INTO xp_transactions (user_id, transaction_type, amount, description, related_lesson_id, related_session_id)
    VALUES (user_id, transaction_type, xp_amount, description, related_lesson_id, related_session_id);
    
    -- Update user's total XP
    UPDATE users 
    SET total_xp = total_xp + xp_amount,
        current_level = FLOOR((total_xp + xp_amount) / 100) + 1  -- Simple level calculation: 100 XP per level
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update user streak
CREATE OR REPLACE FUNCTION update_user_streak(user_id UUID)
RETURNS VOID AS $$
DECLARE
    today_date DATE := CURRENT_DATE;
    yesterday_date DATE := CURRENT_DATE - INTERVAL '1 day';
    streak_exists BOOLEAN;
    yesterday_streak_exists BOOLEAN;
BEGIN
    -- Check if today's streak already exists
    SELECT EXISTS(SELECT 1 FROM user_streaks WHERE user_id = update_user_streak.user_id AND streak_date = today_date)
    INTO streak_exists;
    
    -- Check if yesterday's streak exists
    SELECT EXISTS(SELECT 1 FROM user_streaks WHERE user_id = update_user_streak.user_id AND streak_date = yesterday_date)
    INTO yesterday_streak_exists;
    
    -- Insert or update today's streak
    INSERT INTO user_streaks (user_id, streak_date, activities_completed, xp_earned)
    VALUES (user_id, today_date, 1, 0)
    ON CONFLICT (user_id, streak_date)
    DO UPDATE SET activities_completed = user_streaks.activities_completed + 1;
    
    -- Update user's current streak
    IF yesterday_streak_exists THEN
        -- Continue streak
        UPDATE users SET current_streak = current_streak + 1 WHERE id = user_id;
    ELSE
        -- Start new streak
        UPDATE users SET current_streak = 1 WHERE id = user_id;
    END IF;
    
    -- Update longest streak if current streak is longer
    UPDATE users 
    SET longest_streak = GREATEST(longest_streak, current_streak)
    WHERE id = user_id;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- COMMENTS AND DOCUMENTATION
-- =============================================================================

-- User creation handled by Supabase Auth system
COMMENT ON FUNCTION award_xp IS 'Awards XP to a user and updates their level';
COMMENT ON FUNCTION update_user_streak IS 'Updates user streak data for daily activities';
