# Database Deployment Guide

This guide provides step-by-step instructions for deploying the TaraDental database schema to your Supabase project.

## 🎯 Prerequisites

- Access to Supabase project: **TaraDental Beta MVP**
- Project URL: https://supabase.com/dashboard/project/xxfbtyephmthfnvystzt
- Admin access to the Supabase dashboard

## 📋 Deployment Checklist

- [ ] Backup existing data (if any)
- [ ] Apply initial schema migration
- [ ] Load seed data (optional)
- [ ] Verify deployment
- [ ] Test basic operations

⚠️ **Note**: RLS policies are already configured in your Supabase project and should not be modified.

## 🚀 Step-by-Step Deployment

### Step 1: Access Supabase SQL Editor

1. **Navigate to your project**:
   - Go to https://supabase.com/dashboard/project/xxfbtyephmthfnvystzt
   - Click on "SQL Editor" in the left sidebar

2. **Create a new query**:
   - Click "New Query" button
   - You'll use this to run the migration scripts

### Step 2: Apply Initial Schema

1. **Open the migration file**:
   - Copy the contents of `database/migrations/001_initial_schema.sql`

2. **Execute the migration**:
   ```sql
   -- Paste the entire contents of 001_initial_schema.sql here
   -- This will create all tables, indexes, triggers, and types
   ```

3. **Verify execution**:
   - Check that the query executed without errors
   - You should see "Success. No rows returned" or similar

4. **Verify tables were created**:
   ```sql
   SELECT table_name 
   FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name IN (
       'users', 'daily_lessons', 'lesson_progress', 
       'chat_sessions', 'chat_messages', 'patient_personas',
       'objection_scenarios', 'roleplay_sessions', 
       'xp_transactions', 'user_streaks', 'schema_migrations'
   )
   ORDER BY table_name;
   ```
   
   Expected result: 11 tables should be listed.

### Step 3: Verify Existing RLS Configuration

⚠️ **Important**: RLS policies are already configured in your Supabase project. Do not modify them.

1. **Verify RLS is enabled** (read-only check):
   ```sql
   SELECT schemaname, tablename, rowsecurity
   FROM pg_tables
   WHERE schemaname = 'public'
   AND tablename IN (
       'users', 'daily_lessons', 'lesson_progress',
       'chat_sessions', 'chat_messages', 'patient_personas',
       'objection_scenarios', 'roleplay_sessions',
       'xp_transactions', 'user_streaks'
   );
   ```

   Expected result: All tables should show `rowsecurity = true`.

### Step 4: Load Seed Data (Optional)

⚠️ **Note**: Only run this in development environments or if you want sample data.

1. **Open seed data file**:
   - Copy the contents of `database/seed-data.sql`

2. **Execute seed data**:
   ```sql
   -- Paste the entire contents of seed-data.sql here
   -- This will add sample lessons, personas, and scenarios
   ```

3. **Verify seed data**:
   ```sql
   SELECT 'daily_lessons' as table_name, count(*) as row_count FROM daily_lessons
   UNION ALL
   SELECT 'patient_personas', count(*) FROM patient_personas
   UNION ALL
   SELECT 'objection_scenarios', count(*) FROM objection_scenarios;
   ```
   
   Expected result: Should show 3 lessons, 3 personas, and 3 scenarios.

### Step 5: Verify Deployment

1. **Check migration status**:
   ```sql
   SELECT * FROM schema_migrations ORDER BY version;
   ```
   
   Expected result: Should show version '001' with applied timestamp.

2. **Test table structure**:
   ```sql
   -- Check users table structure
   SELECT column_name, data_type, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'users' 
   ORDER BY ordinal_position;
   ```

3. **Test RLS policies**:
   ```sql
   -- This should work (viewing public lessons)
   SELECT id, title, difficulty FROM daily_lessons WHERE is_active = true LIMIT 1;
   
   -- This should return empty (no user context)
   SELECT id, email FROM users LIMIT 1;
   ```

### Step 6: Test Basic Operations

1. **Test utility functions**:
   ```sql
   -- Test XP award function exists
   SELECT routine_name 
   FROM information_schema.routines 
   WHERE routine_name IN ('award_xp', 'update_user_streak', 'create_sample_user');
   ```

2. **Test triggers**:
   ```sql
   -- Check that triggers were created
   SELECT trigger_name, event_manipulation, event_object_table 
   FROM information_schema.triggers 
   WHERE trigger_schema = 'public'
   AND trigger_name LIKE '%updated_at%';
   ```

## 🔍 Troubleshooting

### Common Issues

1. **"relation already exists" errors**:
   - This is normal if re-running migrations
   - The migration uses `IF NOT EXISTS` clauses to handle this

2. **Permission denied errors**:
   - Ensure you're using the service role key
   - Check that you have admin access to the project

3. **RLS policy conflicts**:
   - Drop existing policies if needed:
     ```sql
     DROP POLICY IF EXISTS "policy_name" ON table_name;
     ```

4. **Foreign key constraint errors**:
   - Ensure tables are created in the correct order
   - Check that referenced tables exist

### Rollback Procedures

If you need to rollback the deployment:

1. **Drop all tables** (⚠️ **DESTRUCTIVE**):
   ```sql
   DROP TABLE IF EXISTS user_streaks CASCADE;
   DROP TABLE IF EXISTS xp_transactions CASCADE;
   DROP TABLE IF EXISTS roleplay_sessions CASCADE;
   DROP TABLE IF EXISTS objection_scenarios CASCADE;
   DROP TABLE IF EXISTS patient_personas CASCADE;
   DROP TABLE IF EXISTS chat_messages CASCADE;
   DROP TABLE IF EXISTS chat_sessions CASCADE;
   DROP TABLE IF EXISTS lesson_progress CASCADE;
   DROP TABLE IF EXISTS daily_lessons CASCADE;
   DROP TABLE IF EXISTS users CASCADE;
   DROP TABLE IF EXISTS schema_migrations CASCADE;
   ```

2. **Drop custom types**:
   ```sql
   DROP TYPE IF EXISTS xp_transaction_type CASCADE;
   DROP TYPE IF EXISTS message_type CASCADE;
   DROP TYPE IF EXISTS session_status CASCADE;
   DROP TYPE IF EXISTS difficulty_level CASCADE;
   DROP TYPE IF EXISTS lesson_type CASCADE;
   ```

## ✅ Post-Deployment Verification

After successful deployment, verify these items:

### Database Structure
- [ ] All 10 core tables created
- [ ] All indexes created
- [ ] All triggers active
- [ ] Custom types defined

### Security
- [ ] RLS enabled on all tables
- [ ] Policies created and active
- [ ] User data isolation working
- [ ] Public data accessible

### Data
- [ ] Seed data loaded (if applicable)
- [ ] Sample queries working
- [ ] Utility functions available

### Performance
- [ ] Indexes created successfully
- [ ] Query performance acceptable
- [ ] No obvious bottlenecks

## 📞 Support

If you encounter issues during deployment:

1. **Check Supabase logs**:
   - Go to Logs section in Supabase dashboard
   - Look for error messages

2. **Verify SQL syntax**:
   - Ensure no copy-paste errors
   - Check for missing semicolons

3. **Contact support**:
   - Supabase community: https://github.com/supabase/supabase/discussions
   - Project documentation: See `database/README.md`

## 🎉 Success!

Once deployment is complete, your TaraDental database is ready for:

- User authentication and profiles
- Lesson content management
- Progress tracking
- Chat with Tara functionality
- Roleplay simulations
- XP and gamification features

Next steps: Configure your application environment variables and test the connection from your frontend and backend applications.
