# TaraDental Database Schema

This directory contains the complete database schema and related files for the TaraDental platform.

## 📁 File Structure

```
database/
├── schema.sql              # Complete database schema
├── seed-data.sql          # Initial data for development
├── migrations/            # Database migration files
│   └── 001_initial_schema.sql
├── DEPLOYMENT.md          # Deployment guide
└── README.md              # This documentation
```

## 🗄️ Database Overview

The TaraDental database is built on PostgreSQL (via Supabase) and includes:

- **10 core tables** for user data, lessons, chat, and roleplay
- **Row Level Security (RLS)** for data protection
- **Custom types and enums** for data consistency
- **Indexes** for optimal performance
- **Triggers** for automatic updates
- **Utility functions** for common operations

## 📊 Table Structure

### Core Tables

| Table | Purpose | Key Features |
|-------|---------|--------------|
| `users` | User profiles and progress | XP tracking, streaks, preferences |
| `daily_lessons` | Lesson content and structure | JSONB content, difficulty levels |
| `lesson_progress` | User lesson completion | Progress tracking, scoring |
| `chat_sessions` | Chat with Tara sessions | Session management |
| `chat_messages` | Individual chat messages | Message types, metadata |
| `patient_personas` | AI patient personalities | Personality traits, voice settings |
| `objection_scenarios` | Roleplay scenarios | LAARC guidance, categories |
| `roleplay_sessions` | User roleplay attempts | Performance scoring, feedback |
| `xp_transactions` | XP earning history | Transaction types, amounts |
| `user_streaks` | Daily activity tracking | Streak maintenance |

## 🔧 Setup Instructions

### 1. Apply Schema to Supabase

1. **Open Supabase SQL Editor**:
   - Go to https://supabase.com/dashboard/project/xxfbtyephmthfnvystzt
   - Navigate to SQL Editor

2. **Run Migration**:
   ```sql
   -- Copy and paste the contents of migrations/001_initial_schema.sql
   -- This will create all tables, indexes, and triggers
   ```

3. **Verify RLS Policies**:
   - RLS policies are already configured in your Supabase project
   - Do not modify existing policies

4. **Load Seed Data** (Optional):
   ```sql
   -- Copy and paste the contents of seed-data.sql
   -- This will add sample lessons, personas, and scenarios
   ```

### 2. Verify Installation

Run this query to verify all tables were created:

```sql
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'users', 'daily_lessons', 'lesson_progress',
    'chat_sessions', 'chat_messages', 'patient_personas',
    'objection_scenarios', 'roleplay_sessions',
    'xp_transactions', 'user_streaks'
);
```

## 🔒 Security Features

### Row Level Security (RLS)

All tables have RLS enabled with existing policies that ensure:

- **Users can only access their own data**
- **Public content** (lessons, personas, scenarios) is readable by authenticated users
- **Data isolation** between users is maintained
- **Existing policies should not be modified**

## 📈 Performance Optimizations

### Indexes

Strategic indexes are created for:

- **User lookups** by email and ID
- **Lesson ordering** and filtering
- **Progress tracking** queries
- **Chat message** retrieval
- **Roleplay session** history
- **XP transaction** history

## 🔄 Data Types and Enums

### Custom Types

```sql
-- Lesson types
lesson_type: 'video' | 'quiz' | 'challenge' | 'reading'

-- Difficulty levels
difficulty_level: 'beginner' | 'intermediate' | 'advanced'

-- Session status
session_status: 'active' | 'completed' | 'abandoned'

-- Message types
message_type: 'user' | 'assistant' | 'system'

-- XP transaction types
xp_transaction_type: 'lesson_complete' | 'quiz_pass' | 'challenge_complete' |
                     'streak_bonus' | 'roleplay_complete' | 'daily_login'
```

## 🛠️ Utility Functions

### XP Management

```sql
-- Award XP to a user
SELECT award_xp(user_id, amount, transaction_type, description);

-- Update user streak
SELECT update_user_streak(user_id);
```

## 📝 JSONB Schema Examples

### Lesson Content Structure

```json
{
  "sections": [
    {
      "type": "video",
      "title": "Introduction",
      "content": "Video description",
      "duration": 300
    },
    {
      "type": "quiz",
      "title": "Knowledge Check",
      "questions": [
        {
          "question": "What is...?",
          "options": ["A", "B", "C", "D"],
          "correct": 1
        }
      ]
    }
  ]
}
```

## 🚀 Next Steps

After applying the schema:

1. ✅ **Schema Applied**: All tables and policies created
2. 🔄 **Environment Configuration**: Set up connection strings
3. 🔄 **Backend Integration**: Implement database models
4. 🔄 **Frontend Integration**: Connect to Supabase client
5. 🔄 **Testing**: Verify all operations work correctly
