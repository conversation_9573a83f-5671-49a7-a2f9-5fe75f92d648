{"name": "taradental-backend", "version": "1.0.0", "description": "Node.js TypeScript API server for the TaraDental platform.", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint src/**/*.ts", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.50.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "elevenlabs-node": "^1.1.8", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.12.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/dotenv": "^6.1.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "keywords": ["dental", "sales-training", "ai", "backend", "api"], "author": "TaraDental Team", "license": "MIT"}