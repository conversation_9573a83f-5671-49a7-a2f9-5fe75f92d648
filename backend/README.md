# TaraDental Backend

Node.js TypeScript API server for the TaraDental platform.

## 🚀 Quick Start

### Development Server
```bash
npm install
npm run dev
```
- **Server URL**: `http://localhost:3001`
- **Health Check**: `http://localhost:3001/health`
- **API Endpoint**: `http://localhost:3001/api`
- **Auto-restart**: Enabled with nodemon

### Production Build
```bash
npm run build    # Compile TypeScript to JavaScript
npm start        # Run compiled server
```

## 📁 Project Structure

```
backend/
├── src/
│   ├── controllers/    # HTTP request handlers
│   ├── services/       # Business logic layer
│   ├── models/         # Data models and types
│   ├── middleware/     # Express middleware functions
│   ├── routes/         # API route definitions
│   ├── utils/          # Utility functions
│   ├── config/         # Configuration management
│   └── server.ts       # Main server entry point
├── dist/               # Compiled JavaScript output
├── package.json        # Dependencies and scripts
├── tsconfig.json       # TypeScript configuration
├── nodemon.json        # Nodemon configuration
└── .eslintrc.json      # ESLint configuration
```

## 🛠️ Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Compile TypeScript to JavaScript |
| `npm start` | Run production server |
| `npm run lint` | Run ESLint code analysis |
| `npm test` | Run unit tests |

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the backend directory:

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# AI Services
OPENAI_API_KEY=your_openai_api_key
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Security
JWT_SECRET=your_jwt_secret
```

### TypeScript Configuration
- **Target**: ES2022
- **Module**: CommonJS
- **Strict mode**: Enabled
- **Source maps**: Enabled for debugging
- **Path mapping**: Configured for clean imports

## 🌐 API Endpoints

### Health & Status
- `GET /health` - Server health check
- `GET /api` - API information and available endpoints

### Authentication (Coming Soon)
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Lessons (Coming Soon)
- `GET /api/lessons` - Get daily lessons
- `POST /api/lessons/:id/complete` - Mark lesson as complete

### Chat (Coming Soon)
- `POST /api/chat` - Chat with Tara AI
- `GET /api/chat/history` - Get chat history

## 🧪 Testing

```bash
npm test                 # Run all tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage report
```

## 🔍 Development Tools

### Code Quality
- **ESLint**: Code linting and style enforcement
- **TypeScript**: Static type checking
- **Prettier**: Code formatting (recommended)

### Debugging
- Source maps enabled for debugging compiled code
- VS Code debugging configuration available
- Console logging with Morgan middleware

### Hot Reload
- **Nodemon**: Automatically restarts server on file changes
- **ts-node**: Direct TypeScript execution without compilation

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Start Production Server
```bash
npm start
```

### Docker (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Follow TypeScript best practices
2. Use ESLint configuration
3. Write tests for new features
4. Update API documentation
5. Follow conventional commit messages

## 📚 Dependencies

### Production
- **express**: Web framework
- **cors**: Cross-origin resource sharing
- **helmet**: Security middleware
- **morgan**: HTTP request logger
- **@supabase/supabase-js**: Database client
- **openai**: OpenAI API client
- **elevenlabs-node**: ElevenLabs API client
- **joi**: Data validation
- **jsonwebtoken**: JWT authentication
- **multer**: File upload handling

### Development
- **typescript**: TypeScript compiler
- **ts-node**: TypeScript execution
- **nodemon**: Development server
- **eslint**: Code linting
- **@types/***: TypeScript type definitions
