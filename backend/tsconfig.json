{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["node", "jest"], "lib": ["ES2022"], "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/config/*": ["./config/*"], "@/controllers/*": ["./controllers/*"], "@/middleware/*": ["./middleware/*"], "@/models/*": ["./models/*"], "@/routes/*": ["./routes/*"], "@/services/*": ["./services/*"], "@/utils/*": ["./utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}