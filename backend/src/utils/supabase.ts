import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';

// Create Supabase client for regular operations (respects RLS)
export const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'taradental-backend',
    },
  },
});

// Export types for TypeScript support
export type { User, Session, AuthError } from '@supabase/supabase-js';

// Helper function to verify JWT token
export const verifyToken = async (token: string) => {
  try {
    const { data: { user }, error } = await supabase.auth.getUser(token);
    if (error) {
      console.error('Error verifying token:', error);
      return null;
    }
    return user;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
};

// Note: Admin operations are not available with anon key
// User management is handled through Supabase Auth UI or client-side operations

// Export default client
export default supabase;
