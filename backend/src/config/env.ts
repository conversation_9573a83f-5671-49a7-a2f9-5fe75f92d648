/**
 * Backend Environment Configuration
 * Type-safe environment variable loading and validation
 */

import { config } from 'dotenv';

// Load .env file
config();

// Environment variable interface
interface EnvironmentConfig {
  // Server Configuration
  server: {
    port: number;
    environment: 'development' | 'production' | 'test';
  };
  
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
  };
  
  // Authentication & Security
  auth: {
    jwtSecret: string;
    jwtExpiresIn: string;
    sessionSecret?: string;
  };
  
  // AI Services
  ai: {
    openai: {
      apiKey: string;
      model: string;
      maxTokens: number;
    };
    elevenlabs: {
      apiKey: string;
      voiceId?: string;
    };
  };
  
  // CORS Configuration
  cors: {
    origins: string[];
  };
  
  // Rate Limiting
  rateLimit: {
    windowMinutes: number;
    maxRequests: number;
  };
  
  // File Upload
  upload: {
    maxFileSize: number;
    allowedTypes: string[];
  };
  
  // Logging
  logging: {
    level: 'error' | 'warn' | 'info' | 'debug';
    enableRequestLogging: boolean;
  };
  
  // Development Settings
  development: {
    debugMode: boolean;
    useMockData: boolean;
    enableApiDocs: boolean;
  };
}

// Validation helper functions
const validateRequired = (value: string | undefined, name: string): string => {
  if (!value || value.trim() === '') {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value.trim();
};

const validateOptional = (value: string | undefined, defaultValue: string): string => {
  return value?.trim() || defaultValue;
};

const validateNumber = (value: string | undefined, defaultValue: number, name: string): number => {
  if (!value) return defaultValue;
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    throw new Error(`Invalid number format for ${name}: ${value}`);
  }
  return num;
};

const validateBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
};

const validateUrl = (value: string | undefined, name: string): string => {
  const url = validateRequired(value, name);
  try {
    new URL(url);
    return url;
  } catch {
    throw new Error(`Invalid URL format for ${name}: ${url}`);
  }
};

const validateArray = (value: string | undefined, defaultValue: string[]): string[] => {
  if (!value) return defaultValue;
  return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
};

// Load and validate environment variables
const loadEnvironmentConfig = (): EnvironmentConfig => {
  try {
    // Server Configuration
    const port = validateNumber(process.env.PORT, 3001, 'PORT');
    const environment = validateOptional(process.env.NODE_ENV, 'development') as 'development' | 'production' | 'test';
    
    // Supabase Configuration (Required)
    const supabaseUrl = validateUrl(process.env.SUPABASE_URL, 'SUPABASE_URL');
    const supabaseAnonKey = validateRequired(process.env.SUPABASE_ANON_KEY, 'SUPABASE_ANON_KEY');
    
    // Authentication & Security
    const jwtSecret = validateRequired(process.env.JWT_SECRET, 'JWT_SECRET');
    const jwtExpiresIn = validateOptional(process.env.JWT_EXPIRES_IN, '7d');
    const sessionSecret = process.env.SESSION_SECRET;
    
    // AI Services
    const openaiApiKey = validateRequired(process.env.OPENAI_API_KEY, 'OPENAI_API_KEY');
    const openaiModel = validateOptional(process.env.OPENAI_MODEL, 'gpt-4');
    const openaiMaxTokens = validateNumber(process.env.OPENAI_MAX_TOKENS, 1000, 'OPENAI_MAX_TOKENS');
    
    const elevenlabsApiKey = validateRequired(process.env.ELEVENLABS_API_KEY, 'ELEVENLABS_API_KEY');
    const elevenlabsVoiceId = process.env.ELEVENLABS_VOICE_ID;
    
    // CORS Configuration
    const corsOrigins = validateArray(process.env.CORS_ORIGINS, ['http://localhost:5173', 'http://localhost:4173']);
    
    // Rate Limiting
    const rateLimitWindow = validateNumber(process.env.RATE_LIMIT_WINDOW, 15, 'RATE_LIMIT_WINDOW');
    const rateLimitMaxRequests = validateNumber(process.env.RATE_LIMIT_MAX_REQUESTS, 100, 'RATE_LIMIT_MAX_REQUESTS');
    
    // File Upload
    const maxFileSize = validateNumber(process.env.MAX_FILE_SIZE, 10485760, 'MAX_FILE_SIZE'); // 10MB default
    const allowedFileTypes = validateArray(process.env.ALLOWED_FILE_TYPES, [
      'image/jpeg', 'image/png', 'image/gif', 'audio/mpeg', 'audio/wav'
    ]);
    
    // Logging
    const logLevel = validateOptional(process.env.LOG_LEVEL, 'info') as 'error' | 'warn' | 'info' | 'debug';
    const enableRequestLogging = validateBoolean(process.env.ENABLE_REQUEST_LOGGING, true);
    
    // Development Settings
    const debugMode = validateBoolean(process.env.DEBUG_MODE, environment === 'development');
    const useMockData = validateBoolean(process.env.USE_MOCK_DATA, false);
    const enableApiDocs = validateBoolean(process.env.ENABLE_API_DOCS, environment === 'development');
    
    return {
      server: {
        port,
        environment,
      },
      supabase: {
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
      },
      auth: {
        jwtSecret,
        jwtExpiresIn,
        sessionSecret,
      },
      ai: {
        openai: {
          apiKey: openaiApiKey,
          model: openaiModel,
          maxTokens: openaiMaxTokens,
        },
        elevenlabs: {
          apiKey: elevenlabsApiKey,
          voiceId: elevenlabsVoiceId,
        },
      },
      cors: {
        origins: corsOrigins,
      },
      rateLimit: {
        windowMinutes: rateLimitWindow,
        maxRequests: rateLimitMaxRequests,
      },
      upload: {
        maxFileSize,
        allowedTypes: allowedFileTypes,
      },
      logging: {
        level: logLevel,
        enableRequestLogging,
      },
      development: {
        debugMode,
        useMockData,
        enableApiDocs,
      },
    };
  } catch (error) {
    console.error('❌ Environment configuration error:', error);
    process.exit(1);
  }
};

// Load configuration
export const env = loadEnvironmentConfig();

// Export individual configurations for convenience
export const serverConfig = env.server;
export const supabaseConfig = env.supabase;
export const authConfig = env.auth;
export const aiConfig = env.ai;
export const corsConfig = env.cors;
export const rateLimitConfig = env.rateLimit;
export const uploadConfig = env.upload;
export const loggingConfig = env.logging;
export const developmentConfig = env.development;

// Development helpers
if (env.development.debugMode) {
  console.log('🔧 Environment Configuration:', {
    environment: env.server.environment,
    port: env.server.port,
    supabaseUrl: env.supabase.url,
    corsOrigins: env.cors.origins,
    features: {
      debugMode: env.development.debugMode,
      useMockData: env.development.useMockData,
      enableApiDocs: env.development.enableApiDocs,
    },
  });
}

// Runtime validation helper
export const validateEnvironment = (): boolean => {
  try {
    // Check critical configurations
    if (!env.supabase.url || !env.supabase.anonKey) {
      throw new Error('Supabase configuration is incomplete');
    }
    
    if (!env.auth.jwtSecret || env.auth.jwtSecret.length < 32) {
      throw new Error('JWT secret must be at least 32 characters long');
    }
    
    if (!env.ai.openai.apiKey || !env.ai.elevenlabs.apiKey) {
      throw new Error('AI service API keys are required');
    }
    
    // Validate URLs are accessible (basic format check)
    new URL(env.supabase.url);
    
    return true;
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    return false;
  }
};

// Export types for use in other modules
export type { EnvironmentConfig };

// Default export
export default env;
