/**
 * Configuration Module Index
 * Centralized configuration exports for the backend
 */

// Export all environment configurations
export {
  env,
  serverConfig,
  supabaseConfig,
  authConfig,
  aiConfig,
  corsConfig,
  rateLimitConfig,
  uploadConfig,
  loggingConfig,
  developmentConfig,
  validateEnvironment,
  type EnvironmentConfig,
} from './env';

// Re-export default configuration
export { default as config } from './env';

// Configuration validation on module load
import { validateEnvironment } from './env';

// Validate environment on startup
if (!validateEnvironment()) {
  console.error('❌ Environment validation failed. Please check your configuration.');
  process.exit(1);
}

console.log('✅ Environment configuration loaded successfully');
