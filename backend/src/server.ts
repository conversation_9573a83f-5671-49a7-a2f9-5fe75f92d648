import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { serverConfig, corsConfig, loggingConfig } from './config';

const app = express();
const PORT = serverConfig.port;

// Middleware
app.use(helmet());
app.use(cors({
  origin: corsConfig.origins,
  credentials: true,
}));
app.use(morgan(loggingConfig.enableRequestLogging ? 'combined' : 'dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'TaraDental Backend API is running',
    timestamp: new Date().toISOString(),
    environment: serverConfig.environment
  });
});

// Basic API routes
app.get('/api', (_req, res) => {
  res.json({
    message: 'Welcome to TaraDental API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      api: '/api'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
    timestamp: new Date().toISOString()
  });
});

// Error handler
app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: serverConfig.environment === 'development' ? err.message : 'Something went wrong',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 TaraDental Backend API server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API endpoint: http://localhost:${PORT}/api`);
  console.log(`🌍 Environment: ${serverConfig.environment}`);
});

export default app;
