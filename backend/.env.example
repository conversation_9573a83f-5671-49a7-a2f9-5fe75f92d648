# TaraDental Backend Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Port for the backend server
PORT=3001

# Node environment (development, production, test)
NODE_ENV=development

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Your Supabase project URL
SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co

# Your Supabase anon/public key
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh4ZmJ0eWVwaG10aGZudnlzdHp0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxMDg3MzksImV4cCI6MjA2NTY4NDczOX0.VyQ-FEl0NBuc5f0A-hgJtot4mdDbPmVWnDqSn4fJLm8

# Note: Only anon key is used - service role key not needed

# =============================================================================
# AUTHENTICATION & SECURITY
# =============================================================================
# JWT secret for token signing (generate a strong random string)
JWT_SECRET=eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MTc1MDE3NjEzNiwiaWF0IjoxNzUwMTc2MTM2fQ.lLnGb09zSLJICCmhPcL9S6c9akHL9inWVoWgAMocfmo

# JWT expiration time
JWT_EXPIRES_IN=7d

# Session secret for express-session (if used)
SESSION_SECRET=your_session_secret_here

# =============================================================================
# AI SERVICES
# =============================================================================
# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

# ElevenLabs API configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=your_preferred_voice_id

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Allowed origins for CORS (comma-separated)
CORS_ORIGINS=http://localhost:5173,http://localhost:4173

# =============================================================================
# RATE LIMITING
# =============================================================================
# Rate limit window in minutes
RATE_LIMIT_WINDOW=15

# Maximum requests per window
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size for uploads (in bytes)
MAX_FILE_SIZE=10485760

# Allowed file types (comma-separated)
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/mpeg,audio/wav

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Enable/disable request logging
ENABLE_REQUEST_LOGGING=true

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================
# Email service configuration (if using external email service)
# EMAIL_SERVICE_API_KEY=your_email_service_api_key
# EMAIL_FROM_ADDRESS=<EMAIL>

# Analytics service configuration (if using external analytics)
# ANALYTICS_API_KEY=your_analytics_api_key

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Enable/disable debug mode
DEBUG_MODE=true

# Enable/disable mock data
USE_MOCK_DATA=false

# Enable/disable API documentation
ENABLE_API_DOCS=true
