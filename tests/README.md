# TaraDental Tests

Comprehensive testing suite for the TaraDental platform.

## Structure

- `frontend/` - Frontend component and unit tests
- `backend/` - Backend service and integration tests
- `e2e/` - End-to-end tests using Playwright

## Running Tests

```bash
# All tests
npm test

# Frontend tests
npm run test:frontend

# Backend tests
npm run test:backend

# E2E tests
npm run test:e2e
```

## Test Strategy

- **Unit Tests**: Individual components and services
- **Integration Tests**: API endpoints and service interactions
- **E2E Tests**: Complete user workflows
- **Performance Tests**: AI response time validation
