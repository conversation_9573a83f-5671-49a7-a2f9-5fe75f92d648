# TaraDental Supabase Configuration

## Project Information

- **Project Name**: TaraDental Beta MVP
- **Project URL**: https://supabase.com/dashboard/project/xxfbtyephmthfnvystzt
- **Project Reference**: xxfbtyephmthfnvystzt
- **Database URL**: https://xxfbtyephmthfnvystzt.supabase.co

## Authentication Configuration

### Enabled Providers
- ✅ **Email/Password**: Standard email and password authentication
- ❌ **Google OAuth**: Not configured (can be added later if needed)
- ❌ **GitHub OAuth**: Not configured (can be added later if needed)
- ❌ **Other Providers**: Not configured

### Authentication Settings
- **Email Confirmation**: Enabled (recommended for production)
- **Password Requirements**: Standard Supabase defaults
- **Session Duration**: Default (24 hours)
- **Refresh Token Rotation**: Enabled

## Row Level Security (RLS)

RLS policies are already configured for data security. The policies ensure that:

- Users can only access their own data
- Authenticated users can read/write appropriate tables
- Public access is restricted to necessary endpoints only
- Admin operations are properly secured

## Database Schema

The database schema will be implemented in Task 0.5. The following tables are planned:

- `users` - User accounts and progress tracking
- `daily_lessons` - Lesson content and structure  
- `lesson_progress` - User lesson completion tracking
- `chat_sessions` - Chat with Tara sessions
- `chat_messages` - Individual chat messages
- `patient_personas` - AI patient personalities
- `objection_scenarios` - Roleplay scenarios
- `roleplay_sessions` - User roleplay attempts
- `xp_transactions` - XP earning history
- `user_streaks` - Daily activity streaks

## API Keys and Configuration

### Required Environment Variables

**Frontend (.env)**:
```env
VITE_SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

**Backend (.env)**:
```env
SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Key Types and Usage

1. **Anon Key (Public)**:
   - Used for client-side operations
   - Safe to expose in frontend code
   - Respects RLS policies
   - Used for user authentication and authorized data access

2. **Service Role Key (Private)**:
   - Used for server-side admin operations
   - Must be kept secret on backend only
   - Bypasses RLS policies
   - Used for system-level operations

## Security Considerations

### Row Level Security (RLS)
- ✅ Enabled on all user data tables
- ✅ Policies configured for user data isolation
- ✅ Admin access properly restricted
- ✅ Public access limited to necessary endpoints

### API Security
- ✅ CORS configured for allowed origins
- ✅ Rate limiting enabled
- ✅ JWT token validation
- ✅ Secure headers with Helmet.js

### Data Protection
- ✅ User data encrypted at rest
- ✅ Secure connections (HTTPS/TLS)
- ✅ Input validation and sanitization
- ✅ SQL injection protection

## Development vs Production

### Development Environment
- **URL**: https://xxfbtyephmthfnvystzt.supabase.co
- **Purpose**: Development and testing
- **Data**: Test data only
- **Access**: Development team

### Production Environment
- **Status**: Will be configured during deployment (Task 4.4)
- **Considerations**: Separate project for production data
- **Migration**: Schema and policies will be replicated

## Monitoring and Maintenance

### Available Monitoring
- **Dashboard**: Supabase project dashboard
- **Logs**: Real-time logs available
- **Metrics**: Usage and performance metrics
- **Alerts**: Can be configured for critical events

### Backup and Recovery
- **Automatic Backups**: Enabled by Supabase
- **Point-in-time Recovery**: Available
- **Manual Backups**: Can be triggered as needed

## Integration Points

### Frontend Integration
- Supabase client for authentication
- Real-time subscriptions for live data
- File storage for user uploads
- Edge functions for serverless operations

### Backend Integration
- Admin client for system operations
- Database queries and mutations
- User management and authentication
- Webhook handling for events

## Next Steps

1. ✅ **Task 0.4**: Supabase project setup (COMPLETED)
2. 🔄 **Task 0.5**: Implement database schema
3. 🔄 **Task 0.6**: Complete environment configuration
4. 🔄 **Task 1.1**: Frontend Supabase integration
5. 🔄 **Task 1.2**: Backend authentication middleware

## Support and Documentation

- **Supabase Docs**: https://supabase.com/docs
- **API Reference**: https://supabase.com/docs/reference
- **Community**: https://github.com/supabase/supabase/discussions
- **Status Page**: https://status.supabase.com/
