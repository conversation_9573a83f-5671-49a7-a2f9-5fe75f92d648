# TaraDental Configuration Guide

This guide explains the configuration system for the TaraDental platform, including environment variables, type-safe configuration modules, and best practices.

## 📁 Configuration Structure

```
frontend/src/config/
├── env.ts              # Frontend environment configuration
└── index.ts            # Configuration exports

backend/src/config/
├── env.ts              # Backend environment configuration
└── index.ts            # Configuration exports

docs/
├── environment-setup.md # Environment setup guide
└── configuration.md    # This file
```

## 🔧 Configuration Modules

### Frontend Configuration (`frontend/src/config/env.ts`)

The frontend configuration module provides type-safe access to environment variables:

```typescript
import { env, supabaseConfig, apiConfig, featureFlags } from '@/config';

// Access Supabase configuration
const { url, anonKey } = supabaseConfig;

// Check feature flags
if (featureFlags.voiceEnabled) {
  // Enable voice features
}

// Get API base URL
const apiUrl = apiConfig.baseUrl;
```

#### Available Configurations

| Configuration | Type | Description |
|---------------|------|-------------|
| `supabaseConfig` | `{url, anonKey}` | Supabase connection settings |
| `apiConfig` | `{baseUrl}` | Backend API configuration |
| `featureFlags` | `{voiceEnabled, analyticsEnabled, debugMode, useMockData}` | Feature toggles |
| `appConfig` | `{environment, baseUrl?, cdnUrl?}` | Application settings |

### Backend Configuration (`backend/src/config/env.ts`)

The backend configuration module provides comprehensive server configuration:

```typescript
import { env, serverConfig, supabaseConfig, aiConfig } from './config';

// Server settings
const port = serverConfig.port;
const environment = serverConfig.environment;

// AI service configuration
const openaiKey = aiConfig.openai.apiKey;
const elevenlabsKey = aiConfig.elevenlabs.apiKey;
```

#### Available Configurations

| Configuration | Type | Description |
|---------------|------|-------------|
| `serverConfig` | `{port, environment}` | Server runtime settings |
| `supabaseConfig` | `{url, anonKey}` | Supabase connection settings |
| `authConfig` | `{jwtSecret, jwtExpiresIn, sessionSecret?}` | Authentication settings |
| `aiConfig` | `{openai, elevenlabs}` | AI service configurations |
| `corsConfig` | `{origins}` | CORS allowed origins |
| `rateLimitConfig` | `{windowMinutes, maxRequests}` | Rate limiting settings |
| `uploadConfig` | `{maxFileSize, allowedTypes}` | File upload constraints |
| `loggingConfig` | `{level, enableRequestLogging}` | Logging configuration |
| `developmentConfig` | `{debugMode, useMockData, enableApiDocs}` | Development features |

## 🔒 Environment Variables

### Frontend Variables (`.env`)

```env
# Supabase Configuration (Required)
VITE_SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# API Configuration
VITE_API_BASE_URL=http://localhost:3001

# Feature Flags
VITE_ENABLE_VOICE_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_DEBUG_MODE=true
VITE_USE_MOCK_DATA=false

# Application Settings
VITE_NODE_ENV=development
VITE_BASE_URL=https://your-domain.com
VITE_CDN_URL=https://cdn.your-domain.com
```

### Backend Variables (`.env`)

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration (Required)
SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here

# Authentication & Security (Required)
JWT_SECRET=your_very_long_random_string_here
JWT_EXPIRES_IN=7d
SESSION_SECRET=your_session_secret_here

# AI Services (Required)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=your_voice_id

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:4173

# Rate Limiting
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,audio/mpeg,audio/wav

# Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true

# Development Settings
DEBUG_MODE=true
USE_MOCK_DATA=false
ENABLE_API_DOCS=true
```

## ✅ Validation and Error Handling

### Automatic Validation

Both frontend and backend configurations include automatic validation:

- **Required variables** are checked on startup
- **URL formats** are validated
- **Number formats** are validated
- **Boolean values** are properly parsed
- **Array values** are split and cleaned

### Error Handling

```typescript
// Frontend validation (non-blocking)
import { validateEnvironment } from '@/config';

if (!validateEnvironment()) {
  console.error('Configuration validation failed');
  // Application continues with warnings
}

// Backend validation (blocking)
import { validateEnvironment } from './config';

if (!validateEnvironment()) {
  console.error('Configuration validation failed');
  process.exit(1); // Stops server startup
}
```

## 🛠️ Usage Examples

### Frontend Component

```typescript
import React from 'react';
import { featureFlags, appConfig } from '@/config';

export const MyComponent: React.FC = () => {
  return (
    <div>
      {featureFlags.voiceEnabled && (
        <VoiceRecorder />
      )}
      
      {appConfig.environment === 'development' && (
        <DebugPanel />
      )}
    </div>
  );
};
```

### Backend Middleware

```typescript
import express from 'express';
import { corsConfig, rateLimitConfig } from './config';

const app = express();

// CORS configuration
app.use(cors({
  origin: corsConfig.origins,
  credentials: true,
}));

// Rate limiting
app.use(rateLimit({
  windowMs: rateLimitConfig.windowMinutes * 60 * 1000,
  max: rateLimitConfig.maxRequests,
}));
```

### AI Service Integration

```typescript
import OpenAI from 'openai';
import { aiConfig } from './config';

const openai = new OpenAI({
  apiKey: aiConfig.openai.apiKey,
});

export const generateResponse = async (prompt: string) => {
  const response = await openai.chat.completions.create({
    model: aiConfig.openai.model,
    messages: [{ role: 'user', content: prompt }],
    max_tokens: aiConfig.openai.maxTokens,
  });
  
  return response.choices[0]?.message?.content;
};
```

## 🔍 Debugging Configuration

### Debug Mode

When `DEBUG_MODE=true` or `VITE_DEBUG_MODE=true`, configuration details are logged to the console:

```
🔧 Environment Configuration: {
  environment: 'development',
  supabaseUrl: 'https://xxfbtyephmthfnvystzt.supabase.co',
  apiBaseUrl: 'http://localhost:3001',
  features: { voiceEnabled: true, debugMode: true }
}
```

### Configuration Validation

```typescript
// Check if configuration is valid
import { validateEnvironment } from './config';

const isValid = validateEnvironment();
console.log('Configuration valid:', isValid);
```

## 🚀 Best Practices

### 1. Environment-Specific Configuration

```typescript
import { appConfig } from '@/config';

const getApiTimeout = () => {
  switch (appConfig.environment) {
    case 'production':
      return 5000;
    case 'development':
      return 10000;
    default:
      return 3000;
  }
};
```

### 2. Feature Flags

```typescript
import { featureFlags } from '@/config';

// Conditional feature loading
const loadVoiceFeatures = async () => {
  if (featureFlags.voiceEnabled) {
    const { VoiceRecorder } = await import('./VoiceRecorder');
    return VoiceRecorder;
  }
  return null;
};
```

### 3. Type Safety

```typescript
import type { EnvironmentConfig } from './config';

// Use configuration types in your functions
const processConfig = (config: EnvironmentConfig['ai']) => {
  // TypeScript ensures config has the correct structure
  return config.openai.apiKey;
};
```

## 🔧 Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```
   Error: Missing required environment variable: VITE_SUPABASE_ANON_KEY
   ```
   Solution: Check your `.env` file and ensure all required variables are set.

2. **Invalid URL Format**
   ```
   Error: Invalid URL format for VITE_SUPABASE_URL: invalid-url
   ```
   Solution: Ensure URLs are properly formatted with protocol (https://).

3. **Configuration Not Loading**
   ```
   Error: Cannot read property 'url' of undefined
   ```
   Solution: Import configuration from the correct path and ensure validation passes.

### Debug Steps

1. **Check Environment Files**
   ```bash
   # Verify .env files exist
   ls -la frontend/.env backend/.env
   ```

2. **Validate Configuration**
   ```typescript
   import { validateEnvironment } from './config';
   console.log('Valid:', validateEnvironment());
   ```

3. **Check Console Output**
   Look for configuration loading messages in the console during startup.

## 📚 Additional Resources

- [Environment Setup Guide](./environment-setup.md)
- [Supabase Configuration](./supabase-setup.md)
- [Vite Environment Variables](https://vitejs.dev/guide/env-and-mode.html)
- [Node.js Environment Variables](https://nodejs.org/en/learn/command-line/how-to-read-environment-variables-from-nodejs)
