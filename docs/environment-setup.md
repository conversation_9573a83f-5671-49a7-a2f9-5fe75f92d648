# Environment Variables Setup Guide

This guide will help you set up the required environment variables for the TaraDental project.

## Quick Setup

1. **Copy the example files:**
   ```bash
   cp frontend/.env.example frontend/.env
   cp backend/.env.example backend/.env
   ```

2. **Get your Supabase credentials:**
   - Visit: https://supabase.com/dashboard/project/xxfbtyephmthfnvystzt
   - Go to Settings → API
   - Copy the Project URL and anon key

3. **Fill in the environment variables as described below**

## Frontend Environment Variables

Edit `frontend/.env`:

```env
# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
VITE_SUPABASE_ANON_KEY=your_actual_anon_key_here

# API Configuration
VITE_API_BASE_URL=http://localhost:3001

# Feature Flags (OPTIONAL)
VITE_ENABLE_VOICE_FEATURES=true
VITE_ENABLE_ANALYTICS=false
VITE_DEBUG_MODE=true
VITE_NODE_ENV=development
VITE_USE_MOCK_DATA=false
```

### Where to get the values:

- **VITE_SUPABASE_URL**: Already set to the correct project URL
- **VITE_SUPABASE_ANON_KEY**: Get from Supabase dashboard → Settings → API → Project API keys → anon/public key

## Backend Environment Variables

Edit `backend/.env`:

```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration (REQUIRED)
SUPABASE_URL=https://xxfbtyephmthfnvystzt.supabase.co
SUPABASE_ANON_KEY=your_actual_anon_key_here
# Note: Only anon key is used - service role key not needed

# Authentication & Security (REQUIRED)
JWT_SECRET=your_very_long_random_string_here_at_least_32_characters
JWT_EXPIRES_IN=7d

# AI Services (REQUIRED for AI features)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000

ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
ELEVENLABS_VOICE_ID=your_preferred_voice_id

# CORS Configuration
CORS_ORIGINS=http://localhost:5173,http://localhost:4173

# Optional Settings
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100
MAX_FILE_SIZE=10485760
LOG_LEVEL=info
DEBUG_MODE=true
```

### Where to get the values:

- **SUPABASE_URL**: Already set to the correct project URL
- **SUPABASE_ANON_KEY**: Same as frontend anon key (only anon key is used)
- **JWT_SECRET**: Generate a random string (at least 32 characters)
- **OPENAI_API_KEY**: Get from https://platform.openai.com/api-keys
- **ELEVENLABS_API_KEY**: Get from https://elevenlabs.io/app/settings/api-keys

## Security Notes

### ⚠️ Important Security Guidelines

1. **Never commit `.env` files to version control**
   - The `.env` files are already in `.gitignore`
   - Only commit `.env.example` files

2. **Use only anon key**
   - Only the `SUPABASE_ANON_KEY` is used for this project
   - Service role key is not needed or used

3. **Generate strong JWT secrets**
   - Use a random string generator
   - At least 32 characters long
   - Include letters, numbers, and special characters

4. **API key security**
   - Keep OpenAI and ElevenLabs keys on backend only
   - Never expose them in frontend environment variables

## Generating Secure Values

### JWT Secret
```bash
# Generate a secure JWT secret (32+ characters)
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

### Session Secret
```bash
# Generate a secure session secret
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

## Validation

After setting up your environment variables, you can validate them:

### Frontend Validation
```bash
cd frontend
npm run dev
```
- Check browser console for any missing environment variable errors

### Backend Validation
```bash
cd backend
npm run dev
```
- Check terminal output for any missing environment variable errors
- Visit http://localhost:3001/health to verify server is running

## Troubleshooting

### Common Issues

1. **"Missing VITE_SUPABASE_URL" error**
   - Make sure the `.env` file is in the `frontend/` directory
   - Verify the variable name starts with `VITE_`

2. **"Missing SUPABASE_URL" error**
   - Make sure the `.env` file is in the `backend/` directory
   - Verify the variable name does NOT start with `VITE_`

3. **Authentication errors**
   - Double-check your Supabase anon key
   - Verify the project URL is correct
   - Check that RLS policies are properly configured

4. **CORS errors**
   - Verify `CORS_ORIGINS` includes your frontend URL
   - Check that both servers are running on correct ports

### Getting Help

If you encounter issues:
1. Check the console/terminal for specific error messages
2. Verify all required environment variables are set
3. Confirm API keys are valid and have proper permissions
4. Review the [Supabase Setup Documentation](./supabase-setup.md)

## Next Steps

After setting up environment variables:
1. ✅ Task 0.4: Supabase project setup (COMPLETED)
2. 🔄 Task 0.5: Implement database schema
3. 🔄 Task 0.6: Complete environment configuration
4. 🔄 Task 1.1: Frontend Supabase integration
