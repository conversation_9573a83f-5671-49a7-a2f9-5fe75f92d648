/**
 * Authentication Type Definitions
 * Type definitions for authentication-related data structures
 */

import type { User, Session, AuthError } from '@supabase/supabase-js';

// Re-export Supabase types
export type { User, Session, AuthError };

// Authentication state interface
export interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
  initialized: boolean;
  error: string | null;
}

// Login form data
export interface LoginFormData {
  email: string;
  password: string;
}

// Signup form data
export interface SignupFormData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
}

// Password reset form data
export interface PasswordResetFormData {
  email: string;
}

// Authentication actions
export interface AuthActions {
  // Authentication operations
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, metadata?: Record<string, any>) => Promise<void>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  
  // Session management
  refreshSession: () => Promise<void>;
  clearError: () => void;
  
  // Internal state management
  setUser: (user: User | null) => void;
  setSession: (session: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setInitialized: (initialized: boolean) => void;
}

// Combined auth store interface
export interface AuthStore extends AuthState, AuthActions {}

// Authentication context type
export interface AuthContextType extends AuthStore {}

// Route protection props
export interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

// Auth form props
export interface AuthFormProps {
  onSubmit?: () => void;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  className?: string;
}

// User profile data
export interface UserProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
}

// Authentication error types
export type AuthErrorType = 
  | 'invalid_credentials'
  | 'email_not_confirmed'
  | 'user_not_found'
  | 'weak_password'
  | 'email_already_exists'
  | 'network_error'
  | 'unknown_error';

// Authentication error with type
export interface TypedAuthError {
  type: AuthErrorType;
  message: string;
  originalError?: AuthError;
}
