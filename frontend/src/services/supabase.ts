import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';

// Create Supabase client
export const supabase = createClient(supabaseConfig.url, supabaseConfig.anonKey, {
  auth: {
    // Configure authentication settings
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Storage key for session persistence
    storageKey: 'taradental-auth-token',
  },
  // Configure real-time settings
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  // Configure global settings
  global: {
    headers: {
      'X-Client-Info': 'taradental-frontend',
    },
  },
});

// Export types for TypeScript support
export type { User, Session, AuthError } from '@supabase/supabase-js';

// Helper function to get current user
export const getCurrentUser = async () => {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) {
    console.error('Error getting current user:', error);
    return null;
  }
  return user;
};

// Helper function to get current session
export const getCurrentSession = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  if (error) {
    console.error('Error getting current session:', error);
    return null;
  }
  return session;
};

// Helper function to sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Helper function to check if user is authenticated
export const isAuthenticated = async (): Promise<boolean> => {
  const session = await getCurrentSession();
  return !!session;
};

// Helper function to sign in with email and password
export const signInWithPassword = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email: email.trim().toLowerCase(),
    password,
  });

  if (error) {
    console.error('Error signing in:', error);
    throw error;
  }

  return data;
};

// Helper function to sign up with email and password
export const signUpWithPassword = async (email: string, password: string, metadata?: Record<string, any>) => {
  const { data, error } = await supabase.auth.signUp({
    email: email.trim().toLowerCase(),
    password,
    options: {
      data: metadata,
    },
  });

  if (error) {
    console.error('Error signing up:', error);
    throw error;
  }

  return data;
};

// Helper function to reset password
export const resetPasswordForEmail = async (email: string) => {
  const { data, error } = await supabase.auth.resetPasswordForEmail(email.trim().toLowerCase(), {
    redirectTo: `${window.location.origin}/reset-password`,
  });

  if (error) {
    console.error('Error resetting password:', error);
    throw error;
  }

  return data;
};

// Helper function to update user password
export const updatePassword = async (password: string) => {
  const { data, error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    console.error('Error updating password:', error);
    throw error;
  }

  return data;
};

// Helper function to update user metadata
export const updateUserMetadata = async (metadata: Record<string, any>) => {
  const { data, error } = await supabase.auth.updateUser({
    data: metadata,
  });

  if (error) {
    console.error('Error updating user metadata:', error);
    throw error;
  }

  return data;
};

// Helper function to refresh session
export const refreshSession = async () => {
  const { data, error } = await supabase.auth.refreshSession();

  if (error) {
    console.error('Error refreshing session:', error);
    throw error;
  }

  return data;
};

// Export default client
export default supabase;
