/**
 * Authentication Store
 * Zustand store for managing authentication state and operations
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { supabase } from '../services/supabase';
import type { AuthStore, AuthState, TypedAuthError, AuthErrorType } from '../types/auth';
import type { User, Session, AuthError } from '@supabase/supabase-js';

// Initial state
const initialState: AuthState = {
  user: null,
  session: null,
  loading: false,
  initialized: false,
  error: null,
};

// Helper function to parse auth errors
const parseAuthError = (error: AuthError): TypedAuthError => {
  let type: AuthErrorType = 'unknown_error';
  let message = error.message;

  // Map common Supabase auth errors to our types
  if (error.message.includes('Invalid login credentials')) {
    type = 'invalid_credentials';
    message = 'Invalid email or password. Please check your credentials and try again.';
  } else if (error.message.includes('Email not confirmed')) {
    type = 'email_not_confirmed';
    message = 'Please check your email and click the confirmation link before signing in.';
  } else if (error.message.includes('User not found')) {
    type = 'user_not_found';
    message = 'No account found with this email address.';
  } else if (error.message.includes('Password should be at least')) {
    type = 'weak_password';
    message = 'Password must be at least 6 characters long.';
  } else if (error.message.includes('User already registered')) {
    type = 'email_already_exists';
    message = 'An account with this email already exists. Please sign in instead.';
  } else if (error.message.includes('fetch')) {
    type = 'network_error';
    message = 'Network error. Please check your connection and try again.';
  }

  return { type, message, originalError: error };
};

// Create the auth store
export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      (set) => ({
        // Initial state
        ...initialState,

        // Authentication operations
        login: async (email: string, password: string) => {
          set({ loading: true, error: null });
          
          try {
            const { data, error } = await supabase.auth.signInWithPassword({
              email: email.trim().toLowerCase(),
              password,
            });

            if (error) {
              const typedError = parseAuthError(error);
              set({ error: typedError.message, loading: false });
              throw new Error(typedError.message);
            }

            if (data.user && data.session) {
              set({
                user: data.user,
                session: data.session,
                loading: false,
                error: null,
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Login failed';
            set({ error: errorMessage, loading: false });
            throw error;
          }
        },

        signup: async (email: string, password: string, metadata = {}) => {
          set({ loading: true, error: null });
          
          try {
            const { data, error } = await supabase.auth.signUp({
              email: email.trim().toLowerCase(),
              password,
              options: {
                data: metadata,
              },
            });

            if (error) {
              const typedError = parseAuthError(error);
              set({ error: typedError.message, loading: false });
              throw new Error(typedError.message);
            }

            // Note: User might need to confirm email before session is created
            if (data.user) {
              set({
                user: data.user,
                session: data.session,
                loading: false,
                error: null,
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Signup failed';
            set({ error: errorMessage, loading: false });
            throw error;
          }
        },

        logout: async () => {
          set({ loading: true, error: null });
          
          try {
            const { error } = await supabase.auth.signOut();
            
            if (error) {
              console.error('Logout error:', error);
              // Don't throw on logout errors, just clear local state
            }

            set({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          } catch (error) {
            console.error('Logout error:', error);
            // Clear state even if logout fails
            set({
              user: null,
              session: null,
              loading: false,
              error: null,
            });
          }
        },

        resetPassword: async (email: string) => {
          set({ loading: true, error: null });
          
          try {
            const { error } = await supabase.auth.resetPasswordForEmail(email.trim().toLowerCase(), {
              redirectTo: `${window.location.origin}/reset-password`,
            });

            if (error) {
              const typedError = parseAuthError(error);
              set({ error: typedError.message, loading: false });
              throw new Error(typedError.message);
            }

            set({ loading: false, error: null });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
            set({ error: errorMessage, loading: false });
            throw error;
          }
        },

        refreshSession: async () => {
          try {
            const { data, error } = await supabase.auth.refreshSession();
            
            if (error) {
              console.error('Session refresh error:', error);
              set({ user: null, session: null });
              return;
            }

            if (data.session && data.user) {
              set({
                user: data.user,
                session: data.session,
              });
            }
          } catch (error) {
            console.error('Session refresh error:', error);
            set({ user: null, session: null });
          }
        },

        // Utility actions
        clearError: () => set({ error: null }),

        // Internal state management
        setUser: (user: User | null) => set({ user }),
        setSession: (session: Session | null) => set({ session }),
        setLoading: (loading: boolean) => set({ loading }),
        setError: (error: string | null) => set({ error }),
        setInitialized: (initialized: boolean) => set({ initialized }),
      }),
      {
        name: 'taradental-auth-store',
        partialize: (state) => ({
          // Only persist user and session, not loading/error states
          user: state.user,
          session: state.session,
          initialized: state.initialized,
        }),
      }
    ),
    {
      name: 'auth-store',
    }
  )
);

// Initialize auth state on app start
export const initializeAuth = async () => {
  const { setUser, setSession, setInitialized, setLoading } = useAuthStore.getState();
  
  setLoading(true);
  
  try {
    // Get initial session
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Error getting initial session:', error);
    }

    if (session) {
      setUser(session.user);
      setSession(session);
    }

    // Listen for auth changes
    supabase.auth.onAuthStateChange((event, session) => {
      console.log('Auth state changed:', event, session?.user?.email);
      
      setUser(session?.user ?? null);
      setSession(session);
      
      if (event === 'SIGNED_OUT') {
        // Clear any cached data on sign out
        setUser(null);
        setSession(null);
      }
    });

    setInitialized(true);
  } catch (error) {
    console.error('Auth initialization error:', error);
  } finally {
    setLoading(false);
  }
};
