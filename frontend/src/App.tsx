/**
 * Main Application Component
 * Root component with routing and authentication setup
 */

import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { initializeAuth } from './store/authStore';
import { AuthenticatedRoute, GuestRoute } from './components/auth';
import { LoginPage, SignupPage, DashboardPage } from './pages';

function App() {
  // Initialize authentication on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  return (
    <Router>
      <div className="App">
        <Routes>
          {/* Public routes (guest only) */}
          <Route
            path="/login"
            element={
              <GuestRoute>
                <LoginPage />
              </GuestRoute>
            }
          />
          <Route
            path="/signup"
            element={
              <GuestRoute>
                <SignupPage />
              </GuestRoute>
            }
          />

          {/* Protected routes (authenticated users only) */}
          <Route
            path="/dashboard"
            element={
              <AuthenticatedRoute>
                <DashboardPage />
              </AuthenticatedRoute>
            }
          />

          {/* Default redirects */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
