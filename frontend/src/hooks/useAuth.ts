/**
 * Authentication Hook
 * Custom hook for authentication operations and state management
 */

import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/authStore';
import type { LoginFormData, SignupFormData } from '../types/auth';

export const useAuth = () => {
  const navigate = useNavigate();
  
  // Get auth state and actions from store
  const {
    user,
    session,
    loading,
    initialized,
    error,
    login: storeLogin,
    signup: storeSignup,
    logout: storeLogout,
    resetPassword: storeResetPassword,
    refreshSession,
    clearError,
  } = useAuthStore();

  // Computed values
  const isAuthenticated = !!user && !!session;
  const isLoading = loading || !initialized;

  // Enhanced login with navigation
  const login = useCallback(async (data: LoginFormData, redirectTo = '/dashboard') => {
    try {
      await storeLogin(data.email, data.password);
      navigate(redirectTo);
    } catch (error) {
      // Error is already handled in store
      throw error;
    }
  }, [storeLogin, navigate]);

  // Enhanced signup with navigation
  const signup = useCallback(async (data: SignupFormData, redirectTo = '/dashboard') => {
    try {
      const metadata = {
        firstName: data.firstName,
        lastName: data.lastName,
      };
      
      await storeSignup(data.email, data.password, metadata);
      
      // Check if user needs email confirmation
      const { user: newUser } = useAuthStore.getState();
      if (newUser && !newUser.email_confirmed_at) {
        // Redirect to email confirmation page instead
        navigate('/confirm-email');
      } else {
        navigate(redirectTo);
      }
    } catch (error) {
      // Error is already handled in store
      throw error;
    }
  }, [storeSignup, navigate]);

  // Enhanced logout with navigation
  const logout = useCallback(async (redirectTo = '/login') => {
    try {
      await storeLogout();
      navigate(redirectTo);
    } catch (error) {
      // Even if logout fails, redirect to login
      navigate(redirectTo);
    }
  }, [storeLogout, navigate]);

  // Password reset
  const resetPassword = useCallback(async (email: string) => {
    try {
      await storeResetPassword(email);
      return true;
    } catch (error) {
      throw error;
    }
  }, [storeResetPassword]);

  // Require authentication (for protected routes)
  const requireAuth = useCallback((redirectTo = '/login') => {
    if (initialized && !isAuthenticated) {
      navigate(redirectTo);
      return false;
    }
    return true;
  }, [initialized, isAuthenticated, navigate]);

  // Require guest (redirect authenticated users)
  const requireGuest = useCallback((redirectTo = '/dashboard') => {
    if (initialized && isAuthenticated) {
      navigate(redirectTo);
      return false;
    }
    return true;
  }, [initialized, isAuthenticated, navigate]);

  // Get user display name
  const getUserDisplayName = useCallback(() => {
    if (!user) return null;
    
    const metadata = user.user_metadata;
    if (metadata?.firstName && metadata?.lastName) {
      return `${metadata.firstName} ${metadata.lastName}`;
    }
    if (metadata?.firstName) {
      return metadata.firstName;
    }
    if (metadata?.lastName) {
      return metadata.lastName;
    }
    return user.email;
  }, [user]);

  // Get user initials for avatar
  const getUserInitials = useCallback(() => {
    if (!user) return '';
    
    const metadata = user.user_metadata;
    if (metadata?.firstName && metadata?.lastName) {
      return `${metadata.firstName[0]}${metadata.lastName[0]}`.toUpperCase();
    }
    if (metadata?.firstName) {
      return metadata.firstName[0].toUpperCase();
    }
    if (user.email) {
      return user.email[0].toUpperCase();
    }
    return '';
  }, [user]);

  // Check if user has specific role (for future use)
  const hasRole = useCallback((role: string) => {
    if (!user) return false;
    const userRole = user.user_metadata?.role || user.app_metadata?.role;
    return userRole === role;
  }, [user]);

  // Refresh session periodically
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(() => {
      refreshSession();
    }, 5 * 60 * 1000); // Refresh every 5 minutes

    return () => clearInterval(interval);
  }, [isAuthenticated, refreshSession]);

  return {
    // State
    user,
    session,
    loading: isLoading,
    initialized,
    error,
    isAuthenticated,
    
    // Actions
    login,
    signup,
    logout,
    resetPassword,
    clearError,
    refreshSession,
    
    // Utilities
    requireAuth,
    requireGuest,
    getUserDisplayName,
    getUserInitials,
    hasRole,
  };
};
