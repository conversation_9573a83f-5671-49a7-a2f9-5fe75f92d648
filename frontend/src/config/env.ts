/**
 * Frontend Environment Configuration
 * Type-safe environment variable loading and validation
 */

// Environment variable interface
interface EnvironmentConfig {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
  };
  
  // API Configuration
  api: {
    baseUrl: string;
  };
  
  // Feature Flags
  features: {
    voiceEnabled: boolean;
    analyticsEnabled: boolean;
    debugMode: boolean;
    useMockData: boolean;
  };
  
  // Application Settings
  app: {
    environment: 'development' | 'production' | 'test';
    baseUrl?: string;
    cdnUrl?: string;
  };
}

// Validation helper functions
const validateRequired = (value: string | undefined, name: string): string => {
  if (!value || value.trim() === '') {
    throw new Error(`Missing required environment variable: ${name}`);
  }
  return value.trim();
};

const validateOptional = (value: string | undefined, defaultValue: string): string => {
  return value?.trim() || defaultValue;
};

const validateBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
};

const validateUrl = (value: string | undefined, name: string): string => {
  const url = validateRequired(value, name);
  try {
    new URL(url);
    return url;
  } catch {
    throw new Error(`Invalid URL format for ${name}: ${url}`);
  }
};

// Load and validate environment variables
const loadEnvironmentConfig = (): EnvironmentConfig => {
  try {
    // Supabase Configuration (Required)
    const supabaseUrl = validateUrl(import.meta.env.VITE_SUPABASE_URL, 'VITE_SUPABASE_URL');
    const supabaseAnonKey = validateRequired(import.meta.env.VITE_SUPABASE_ANON_KEY, 'VITE_SUPABASE_ANON_KEY');
    
    // API Configuration
    const apiBaseUrl = validateOptional(import.meta.env.VITE_API_BASE_URL, 'http://localhost:3001');
    
    // Feature Flags
    const voiceEnabled = validateBoolean(import.meta.env.VITE_ENABLE_VOICE_FEATURES, true);
    const analyticsEnabled = validateBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, false);
    const debugMode = validateBoolean(import.meta.env.VITE_DEBUG_MODE, import.meta.env.DEV);
    const useMockData = validateBoolean(import.meta.env.VITE_USE_MOCK_DATA, false);
    
    // Application Settings
    const environment = validateOptional(import.meta.env.VITE_NODE_ENV, import.meta.env.MODE) as 'development' | 'production' | 'test';
    const baseUrl = import.meta.env.VITE_BASE_URL;
    const cdnUrl = import.meta.env.VITE_CDN_URL;
    
    return {
      supabase: {
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
      },
      api: {
        baseUrl: apiBaseUrl,
      },
      features: {
        voiceEnabled,
        analyticsEnabled,
        debugMode,
        useMockData,
      },
      app: {
        environment,
        baseUrl,
        cdnUrl,
      },
    };
  } catch (error) {
    console.error('❌ Environment configuration error:', error);
    throw error;
  }
};

// Load configuration
export const env = loadEnvironmentConfig();

// Export individual configurations for convenience
export const supabaseConfig = env.supabase;
export const apiConfig = env.api;
export const featureFlags = env.features;
export const appConfig = env.app;

// Development helpers
if (env.features.debugMode) {
  console.log('🔧 Environment Configuration:', {
    environment: env.app.environment,
    supabaseUrl: env.supabase.url,
    apiBaseUrl: env.api.baseUrl,
    features: env.features,
  });
}

// Runtime validation helper
export const validateEnvironment = (): boolean => {
  try {
    // Check critical configurations
    if (!env.supabase.url || !env.supabase.anonKey) {
      throw new Error('Supabase configuration is incomplete');
    }
    
    if (!env.api.baseUrl) {
      throw new Error('API base URL is not configured');
    }
    
    // Validate URLs are accessible (basic format check)
    new URL(env.supabase.url);
    new URL(env.api.baseUrl);
    
    return true;
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    return false;
  }
};

// Export types for use in other modules
export type { EnvironmentConfig };

// Default export
export default env;
