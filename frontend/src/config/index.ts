/**
 * Configuration Module Index
 * Centralized configuration exports for the frontend
 */

// Export all environment configurations
export {
  env,
  supabaseConfig,
  apiConfig,
  featureFlags,
  appConfig,
  validateEnvironment,
  type EnvironmentConfig,
} from './env';

// Re-export default configuration
export { default as config } from './env';

// Configuration validation on module load
import { validateEnvironment } from './env';

// Validate environment on startup
if (!validateEnvironment()) {
  console.error('❌ Environment validation failed. Please check your configuration.');
  // Don't exit in frontend, just log the error
}

console.log('✅ Frontend environment configuration loaded successfully');
