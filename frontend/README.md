# TaraDental Frontend

React + TypeScript + Vite application for the TaraDental platform.

## 🚀 Quick Start

### Development Server
```bash
npm install
npm run dev
```
- **Application URL**: `http://localhost:5173`
- **Hot Module Replacement**: Enabled for instant updates
- **Fast Refresh**: React components update without losing state

### Production Build
```bash
npm run build    # Build for production
npm run preview  # Preview production build locally (http://localhost:4173)
```

## 📁 Project Structure

```
frontend/
├── src/
│   ├── components/     # Reusable UI components
│   │   ├── auth/       # Authentication components
│   │   ├── chat/       # Chat interface components
│   │   ├── lessons/    # Lesson components
│   │   └── roleplay/   # Roleplay simulation components
│   ├── pages/          # Page components (routes)
│   ├── hooks/          # Custom React hooks
│   ├── services/       # API services and external integrations
│   ├── store/          # State management (Zustand)
│   ├── types/          # TypeScript type definitions
│   ├── utils/          # Utility functions
│   ├── App.tsx         # Main application component
│   ├── main.tsx        # Application entry point
│   └── index.css       # Global styles (Tailwind CSS)
├── public/             # Static assets
├── dist/               # Production build output
├── package.json        # Dependencies and scripts
├── tsconfig.json       # TypeScript configuration
├── tailwind.config.js  # Tailwind CSS configuration
├── postcss.config.js   # PostCSS configuration
└── vite.config.ts      # Vite configuration
```

## 🛠️ Available Scripts

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server with HMR |
| `npm run build` | Build for production |
| `npm run preview` | Preview production build locally |
| `npm run lint` | Run ESLint code analysis |

## 🎨 Tech Stack

### Core Framework
- **React 19**: Latest React with concurrent features
- **TypeScript**: Static type checking
- **Vite**: Fast build tool and dev server

### Styling
- **Tailwind CSS**: Utility-first CSS framework
- **PostCSS**: CSS processing
- **Autoprefixer**: Automatic vendor prefixes

### State Management
- **Zustand**: Lightweight state management
- **React Hook Form**: Form state management
- **Zod**: Schema validation

### Routing & Navigation
- **React Router DOM**: Client-side routing

### API Integration
- **Axios**: HTTP client for API requests
- **Supabase**: Backend-as-a-Service client

## 🔧 Configuration

### Environment Variables
Create a `.env` file in the frontend directory:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Feature Flags
VITE_ENABLE_VOICE_FEATURES=true
VITE_ENABLE_ANALYTICS=false
```

### Tailwind CSS
- **Configured**: For all source files in `src/`
- **Utilities**: Full Tailwind utility classes available
- **Custom**: Extend configuration in `tailwind.config.js`

### TypeScript
- **Strict mode**: Enabled for better type safety
- **Path mapping**: Clean imports with `@/` prefix
- **React JSX**: Optimized for React 19

## 🌐 Application Features

### Authentication
- User login and registration
- Protected routes
- Session management with Supabase Auth

### Daily Lessons
- Interactive lesson content
- Progress tracking
- XP and gamification system

### Chat with Tara
- Real-time AI chat interface
- Conversation history
- Context-aware responses

### Roleplay Simulation
- AI patient interactions
- Voice integration
- Performance feedback

### Progress Analytics
- Learning progress visualization
- Performance metrics
- Achievement tracking

## 🧪 Testing

```bash
npm run test              # Run unit tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage
```

### Testing Stack (Coming Soon)
- **Vitest**: Fast unit testing
- **React Testing Library**: Component testing
- **MSW**: API mocking
- **Playwright**: E2E testing

## 🔍 Development Tools

### Code Quality
- **ESLint**: Code linting with React and TypeScript rules
- **Prettier**: Code formatting (recommended)
- **TypeScript**: Static type checking

### Development Experience
- **Hot Module Replacement**: Instant updates without page refresh
- **Fast Refresh**: React state preservation during updates
- **Source Maps**: Debugging support
- **VS Code**: Optimized for VS Code development

## 🚀 Deployment

### Build for Production
```bash
npm run build
```
- Output: `dist/` directory
- Optimized: Minified and tree-shaken
- Assets: Hashed filenames for caching

### Preview Production Build
```bash
npm run preview
```
- Local server: `http://localhost:4173`
- Production simulation: Tests production build locally

### Deployment Platforms
- **Vercel**: Recommended (automatic deployments)
- **Netlify**: Static site hosting
- **AWS S3 + CloudFront**: Custom deployment

## 🤝 Contributing

### Code Style
1. Use TypeScript for all new files
2. Follow ESLint configuration
3. Use Tailwind CSS for styling
4. Write tests for components
5. Use conventional commit messages

### Component Guidelines
- Use functional components with hooks
- Implement proper TypeScript types
- Follow React best practices
- Use Zustand for global state
- Implement proper error boundaries

## 📚 Dependencies

### Production
- **react**: UI library
- **react-dom**: React DOM renderer
- **react-router-dom**: Client-side routing
- **zustand**: State management
- **react-hook-form**: Form handling
- **@hookform/resolvers**: Form validation
- **zod**: Schema validation
- **@supabase/supabase-js**: Backend client
- **axios**: HTTP client

### Development
- **vite**: Build tool and dev server
- **typescript**: TypeScript compiler
- **@vitejs/plugin-react**: React plugin for Vite
- **tailwindcss**: CSS framework
- **autoprefixer**: CSS vendor prefixes
- **postcss**: CSS processing
- **eslint**: Code linting
- **@types/***: TypeScript type definitions
