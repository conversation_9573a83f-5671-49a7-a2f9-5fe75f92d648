{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}, {"src": "backend/src/server.ts", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/backend/src/server.ts"}, {"src": "/(.*)", "dest": "/frontend/$1"}], "env": {"NODE_ENV": "production"}, "functions": {"backend/src/server.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}