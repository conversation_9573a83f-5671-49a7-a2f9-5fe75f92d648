{"name": "taradental", "version": "1.0.0", "description": "AI-powered dental sales training platform", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:shared && npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "test:e2e": "cd tests && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "clean": "rm -rf frontend/dist backend/dist shared/dist node_modules", "setup": "npm install && npm run setup:frontend && npm run setup:backend && npm run setup:shared", "setup:frontend": "cd frontend && npm install", "setup:backend": "cd backend && npm install", "setup:shared": "cd shared && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/taradental.git"}, "keywords": ["dental", "sales-training", "ai", "education", "healthcare"], "author": "TaraDental Team", "license": "MIT"}